"""
注解系统模块

提供 Mini-Boot 框架的核心注解定义、组件扫描和注解处理功能.

主要功能:
- 核心注解定义 (@Component, @Service, @Repository 等)
- 依赖注入注解 (@Autowired, @Value 等)
- Web 注解 (@RestController, @RequestMapping 等)
- 配置注解 (@Configuration, @Bean 等)
- 事件注解 (@EventListener 等)
- 调度注解 (@Scheduled 等)
- 组件扫描器
- 注解处理器
"""

# 条件装配注解
from .conditional import (
    AutoConfigureAfter,
    AutoConfigureBefore,
    AutoConfigureOrder,
    Conditional,
    ConditionalOnBean,
    ConditionalOnClass,
    ConditionalOnExpression,
    ConditionalOnMissingBean,
    ConditionalOnMissingClass,
    ConditionalOnProperty,
    ConditionalOnResource,
    ConditionalOnWeb,
    Configuration,
    get_auto_configure_after,
    get_auto_configure_before,
    get_auto_configure_order,
    get_bean_name,
    get_condition_type,
    get_conditional_metadata,
    has_auto_configure_order,
    is_auto_configuration,
    is_bean_factory_method,
    is_conditional,
)

# 配置相关注解
from .config import (
    ComponentScan,
    ConfigurationProperties,
    EnableAutoConfiguration,
    Import,
    Value,
    get_component_scan_metadata,
    get_config_prefix,
    get_configuration_properties_metadata,
    get_import_classes,
    get_scan_packages,
    get_value_expression,
    get_value_metadata,
    is_component_scan,
    is_configuration_properties,
    is_enable_auto_configuration,
    is_import,
    is_value_injection,
)

# 核心注解
from .base import (
    Bean,
    Component,
    Configuration,
    MiniBootApplication,
    Repository,
    Service,
    bean_metadata,
    component_metadata,
    is_bean,
    is_component,
    is_configuration,
    is_miniboot_application,
    is_repository,
    is_service,
)

# 条件评估器
from .evaluator import (
    AutoConfigurationOrderManager,
    ConditionContext,
    ConditionEvaluator,
    create_condition_context,
    evaluate_condition,
)

# Profile 评估器 (从 processor 模块导入)
try:
    from ..processor.profile import (
        ProfileEvaluator,
        create_profile_evaluator,
        evaluate_profile,
    )
except ImportError:
    # 如果 processor 模块不可用，提供占位符
    ProfileEvaluator = None
    create_profile_evaluator = None
    evaluate_profile = None

# 事件相关注解
from .event import (
    AsyncEventListener,
    EventListener,
    EventPublisher,
    event_condition,
    event_order,
    event_type,
    has_injects,
    has_listeners,
    injects,
    is_async_listener,
    is_event_listener,
    listener_info,
    listener_metadata,
    listeners,
    needs_publisher,
)

# 依赖注入注解
from .inject import (
    Autowired,
    DependsOn,
    Inject,
    Lazy,
    Primary,
    Qualifier,
    autowired_metadata,
    depends_on_beans,
    is_autowired,
    is_lazy,
    is_primary,
    is_qualified,
    qualifier_value,
)

# 生命周期管理器
# 生命周期注解
from .lifecycle import (
    Order,
    PostConstruct,
    PreDestroy,
    find_lifecycle_methods,
    get_order_metadata,
    get_order_value,
    get_ordered_methods,
    get_post_construct_metadata,
    get_post_construct_method_name,
    get_pre_destroy_metadata,
    get_pre_destroy_method_name,
    has_lifecycle_methods,
    is_ordered,
    is_post_construct,
    is_pre_destroy,
    sort_by_order,
    sort_instances_by_order,
)

# 元数据管理器
# 元数据类
from .metadata import (
    AsyncMetadata,
    AutowiredMetadata,
    BeanMetadata,
    ComponentMetadata,
    ComponentScanMetadata,
    ConditionalMetadata,
    ConfigurationPropertiesMetadata,
    DependsOnMetadata,
    EventListenerMetadata,
    ImportMetadata,
    LazyMetadata,

    OrderMetadata,
    PostConstructMetadata,
    PreDestroyMetadata,
    PrimaryMetadata,
    QualifierMetadata,
    ScheduledMetadata,
    Scope,
    ValueMetadata,
    create_metadata,
    get_metadata_type,
)

# 性能工具已移除

# Profile 注解
from .profile import (
    Profile,
    get_profile_expression,
    get_profile_metadata,
    get_profile_names,
    is_profile,
)

# 组件扫描器
from .scan import ComponentScanner, ScanFilter, ScanResult

# 定时任务装饰器(额外的)
# 异步和调度注解
from .schedule import (
    Async,
    EnableAsync,
    EnableScheduling,
    Scheduled,
    find_async_methods,
    find_methods,
    get_async_metadata,
    get_async_method_info,
    get_async_pool,
    get_async_timeout,
    get_cron,
    get_fixed_delay,
    get_fixed_rate,
    get_metadata,
    get_method_info,
    has_async_methods,
    has_methods,
    is_async,
    is_async_enabled,
    is_scheduled,
    is_scheduling_enabled,
)

# 作用域注解
from .scope import (
    ApplicationScope,
    PrototypeScope,
    RequestScope,
    SessionScope,
    SingletonScope,
    WebSocketScope,
    get_proxy_mode,
    get_scope_metadata,
    get_scope_value,
    has_application_scope,
    has_prototype_scope,
    has_request_scope,
    has_session_scope,
    has_singleton_scope,
    has_web_scope,
    has_websocket_scope,
    is_scoped,
)

# 验证注解
from .validation import ConfigurationValidator, Max, Min, NotEmpty, NotNull, Pattern, Size, Valid, ValidationError

# Web参数绑定装饰器
# Web装饰器
from .web import (
    Controller,
    DeleteMapping,
    GetMapping,
    PatchMapping,
    PathVariable,
    PostMapping,
    PutMapping,
    RequestBody,
    RequestHeader,
    RequestMapping,
    RequestParam,
    ResponseBody,
    ResponseStatus,
    RestController,
    controller_path,
    has_body,
    has_headers,
    has_params,
    has_path_vars,
    has_response,
    has_route,
    is_controller,
    is_rest_controller,
    path_variables,
    request_body,
    request_headers,
    request_params,
    response_body,
    response_status,
    route_info,
)

__all__ = [
    # 核心注解
    "Component",
    "Service",
    "Repository",
    "Configuration",
    "Bean",
    "MiniBootApplication",
    "is_component",
    "is_service",
    "is_repository",
    "is_configuration",
    "is_bean",
    "is_miniboot_application",
    "component_metadata",
    "bean_metadata",
    # 依赖注入注解
    "Autowired",
    "Inject",
    "Qualifier",
    "Primary",
    "Lazy",
    "DependsOn",
    "is_autowired",
    "is_qualified",
    "is_primary",
    "is_lazy",
    "autowired_metadata",
    "qualifier_value",
    "depends_on_beans",
    # 配置相关注解
    "Value",
    "ConfigurationProperties",
    "ComponentScan",
    "EnableAutoConfiguration",
    "Import",
    "is_value_injection",
    "is_configuration_properties",
    "is_component_scan",
    "is_enable_auto_configuration",
    "is_import",
    "get_value_metadata",
    "get_configuration_properties_metadata",
    "get_component_scan_metadata",
    "get_value_expression",
    "get_config_prefix",
    "get_scan_packages",
    "get_import_classes",
    # 验证注解
    "NotNull",
    "NotEmpty",
    "Min",
    "Max",
    "Size",
    "Pattern",
    "Valid",
    "ConfigurationValidator",
    "ValidationError",
    # 条件装配注解
    "Conditional",
    "ConditionalOnProperty",
    "ConditionalOnBean",
    "ConditionalOnMissingBean",
    "ConditionalOnClass",
    "ConditionalOnMissingClass",
    "ConditionalOnWeb",
    "ConditionalOnExpression",
    "ConditionalOnResource",
    "is_conditional",
    "get_conditional_metadata",
    "get_condition_type",
    # 自动配置注解
    "AutoConfigureAfter",
    "AutoConfigureBefore",
    "AutoConfigureOrder",
    "Configuration",
    "is_auto_configuration",
    "has_auto_configure_order",
    "get_auto_configure_after",
    "get_auto_configure_before",
    "get_auto_configure_order",
    "is_bean_factory_method",
    "get_bean_name",
    # 条件评估器
    "ConditionContext",
    "ConditionEvaluator",
    "create_condition_context",
    "evaluate_condition",
    # 自动配置顺序管理器
    "AutoConfigurationOrderManager",
    # 生命周期注解
    "PostConstruct",
    "PreDestroy",
    "Order",
    "is_post_construct",
    "is_pre_destroy",
    "is_ordered",
    "get_post_construct_metadata",
    "get_pre_destroy_metadata",
    "get_order_metadata",
    "get_post_construct_method_name",
    "get_pre_destroy_method_name",
    "get_order_value",
    "find_lifecycle_methods",
    "has_lifecycle_methods",
    "get_ordered_methods",

    "sort_by_order",
    "sort_instances_by_order",
    # 异步和调度注解
    "Async",
    "Scheduled",
    "EnableAsync",
    "EnableScheduling",
    "is_async",
    "is_scheduled",
    "is_async_enabled",
    "is_scheduling_enabled",
    "get_async_metadata",
    "get_metadata",
    "get_async_pool",
    "get_async_timeout",
    "get_cron",
    "get_fixed_rate",
    "get_fixed_delay",
    "find_async_methods",
    "find_methods",
    "has_async_methods",
    "has_methods",
    "get_async_method_info",
    "get_method_info",
    # 事件相关注解
    "EventListener",
    "AsyncEventListener",
    "EventPublisher",
    "is_event_listener",
    "needs_publisher",
    "is_async_listener",
    "listener_metadata",
    "event_type",
    "event_condition",
    "event_order",
    "listeners",
    "injects",
    "has_listeners",
    "has_injects",
    "listener_info",
    # 组件扫描器
    "ComponentScanner",
    "ScanFilter",
    "ScanResult",
    # 元数据类
    "ComponentMetadata",
    "BeanMetadata",
    "Scope",
    "AutowiredMetadata",
    "ValueMetadata",
    "ConfigurationPropertiesMetadata",
    "ConditionalMetadata",
    "ComponentScanMetadata",
    "PostConstructMetadata",
    "PreDestroyMetadata",
    "AsyncMetadata",
    "ScheduledMetadata",
    "EventListenerMetadata",
    "QualifierMetadata",
    "PrimaryMetadata",
    "LazyMetadata",
    "DependsOnMetadata",
    "OrderMetadata",
    "ImportMetadata",
    "get_metadata_type",
    "create_metadata",


    # Profile 注解
    "Profile",
    "get_profile_expression",
    "get_profile_metadata",
    "get_profile_names",
    "is_profile",
    # Profile 评估器 (从 processor 导入)
    "ProfileEvaluator",
    "create_profile_evaluator",
    "evaluate_profile",
    # 作用域注解
    "Scope",
    "RequestScope",
    "SessionScope",
    "ApplicationScope",
    "WebSocketScope",
    "PrototypeScope",
    "SingletonScope",
    "is_scoped",
    "get_scope_metadata",
    "get_scope_value",
    "get_proxy_mode",
    "has_request_scope",
    "has_session_scope",
    "has_application_scope",
    "has_websocket_scope",
    "has_prototype_scope",
    "has_singleton_scope",
    "has_web_scope",
    # Web装饰器
    "Controller",
    "RestController",
    "RequestMapping",
    "GetMapping",
    "PostMapping",
    "PutMapping",
    "DeleteMapping",
    "PatchMapping",
    "is_controller",
    "is_rest_controller",
    "controller_path",
    "has_route",
    "route_info",
    # 定时任务装饰器
    "Scheduled",
    "EnableScheduling",
    "is_scheduled",
    "is_scheduling_enabled",
    # Web参数绑定装饰器
    "RequestParam",
    "PathVariable",
    "RequestBody",
    "RequestHeader",
    "ResponseBody",
    "ResponseStatus",
    "request_params",
    "path_variables",
    "request_body",
    "request_headers",
    "response_body",
    "response_status",
    "controller_path",
    "route_info",
    "has_params",
    "has_path_vars",
    "has_body",
    "has_headers",
    "has_response",
    "has_route",
]
